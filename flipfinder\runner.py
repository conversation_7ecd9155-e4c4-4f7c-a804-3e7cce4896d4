# import argparse
# import datetime
# import json
# import os
# import sys

# import pandas as pd
# import yaml
# from dateutil import tz

# # Allow running both as a package module and as a standalone script.
# try:
#     from .fetchers import fetch_rss_listings, fetch_muni_table, fetch_muni_links
#     from .filters import dedupe, looks_like_flip
#     from .analyzer import analyze_item
#     from .report import write_outputs, to_markdown_table
#     from .emailer import send_email
# except ImportError:  # pragma: no cover - script execution
#     from fetchers import fetch_rss_listings, fetch_muni_table, fetch_muni_links
#     from filters import dedupe, looks_like_flip
#     from analyzer import analyze_item
#     from report import write_outputs, to_markdown_table
#     from emailer import send_email

# def now_local(tzname):
#     tzinfo = tz.gettz(tzname)
#     return datetime.datetime.now(tzinfo)

# def load_config(path="config.yaml"):
#     cfg_path = os.path.abspath(path)
#     if not os.path.exists(cfg_path):
#         raise FileNotFoundError(f"Missing config file: {cfg_path}")
#     with open(cfg_path, "r", encoding="utf-8") as f:
#         return yaml.safe_load(f)

# def run_city(name, c_cfg, global_cfg):
#     items = []
#     rss = (c_cfg.get("rss_listings") or {})
#     if rss.get("enabled"):
#         items += fetch_rss_listings(rss.get("urls", []))

#     muni = (c_cfg.get("municipal") or {})
#     if muni.get("enabled"):
#         for key in ["tax_sales_url", "permits_url"]:
#             url = muni.get(key)
#             rows = fetch_muni_table(url)
#             if not rows:
#                 rows = fetch_muni_links(url)
#             for r in rows: r.setdefault("city", name)
#             items += rows

#     items = dedupe(items)

#     filtered = []
#     for it in items:
#         if looks_like_flip(it, global_cfg):
#             it["city"] = name
#             # analysis
#             a = analyze_item(it, global_cfg)
#             if a:
#                 it.update(a)
#             filtered.append(it)

#     df = pd.DataFrame(filtered)
#     if not df.empty:
#         # re-order columns
#         order = [
#             "city","keyword_score","price","address","beds","baths","title","description",
#             "url","est_arv","est_reno","closing_costs","carry_costs",
#             "proj_gross_profit","proj_margin_pct","meets_target","source"
#         ]
#         df = df[[c for c in order if c in df.columns] + [c for c in df.columns if c not in order]]
#         df = df.sort_values(by=["meets_target","proj_gross_profit","keyword_score"], ascending=[False, False, False])
#     return df

# def main():
#     parser = argparse.ArgumentParser()
#     parser.add_argument("--cities", help="comma-separated list (default: all in config)")
#     args = parser.parse_args()

#     cfg = load_config()
#     tzname = cfg.get("timezone","America/Toronto")
#     today = now_local(tzname).strftime("%Y-%m-%d")

#     selected = cfg.get("cities") or {}
#     if args.cities:
#         chosen = [c.strip() for c in args.cities.split(",") if c.strip()]
#         selected = {k:v for k,v in selected.items() if k in chosen}

#     per_city = {}
#     combined = []
#     for name, c_cfg in selected.items():
#         df = run_city(name, c_cfg, cfg)
#         per_city[name] = df
#         if df is not None and not df.empty:
#             combined.append(df)

#     out_dir = os.path.join(os.path.dirname(__file__), "..", "output")
#     os.makedirs(out_dir, exist_ok=True)

#     # write per city
#     attachments = []
#     for name, df in per_city.items():
#         csv_path, md_path = write_outputs(out_dir, name, today, df)
#         for pth in [csv_path, md_path]:
#             with open(pth, "rb") as f: data = f.read()
#             ext = pth.split(".")[-1].lower()
#             mime = "text/csv" if ext=="csv" else "text/markdown"
#             attachments.append((os.path.basename(pth), data, mime))

#     # combined
#     combined_df = pd.concat(combined, ignore_index=True) if combined else pd.DataFrame()
#     comb_csv, comb_md = write_outputs(out_dir, "combined", today, combined_df)
#     for pth in [comb_csv, comb_md]:
#         with open(pth, "rb") as f: data = f.read()
#         ext = pth.split(".")[-1].lower()
#         mime = "text/csv" if ext=="csv" else "text/markdown"
#         attachments.append((os.path.basename(pth), data, mime))

#     # email
#     # short HTML summary
#     total = sum(0 if df is None or df.empty else len(df) for df in per_city.values())
#     bullets = "".join([f"<li><b>{name}</b>: {0 if df is None or df.empty else len(df)} deals</li>" for name,df in per_city.items()])
#     html = f"""
#     <h2>Flip Opportunities - {today}</h2>
#     <p><b>Total:</b> {total}</p>
#     <ul>{bullets}</ul>
#     <p>CSV/Markdown attached. Edit <code>config.yaml</code> to add more feeds or cities.</p>
#     """

#     send_email(cfg.get("email",{}), subject=f"Flip Opportunities - {today}", html_body=html, attachments=attachments)
#     print(f"[done] {today}: total opportunities = {total}")

# if __name__ == "__main__":
#     main()

#!/usr/bin/env python3
# Updated runner with:
# - html_listings support (CSS selectors)
# - "new since yesterday" tracking via .state/seen.json
# - --only-new flag for reporting/email
import argparse
import datetime
import json
import os
import sys

import pandas as pd
import yaml
from dateutil import tz

try:
    from . import fetchers as _fetchers
    from .filters import dedupe, looks_like_flip
    from .analyzer import analyze_item
    from .report import write_outputs, to_markdown_table
    from .emailer import send_email
except ImportError:  # pragma: no cover - script execution
    import fetchers as _fetchers
    from filters import dedupe, looks_like_flip
    from analyzer import analyze_item
    from report import write_outputs, to_markdown_table
    from emailer import send_email

fetch_rss_listings = _fetchers.fetch_rss_listings
fetch_muni_table = _fetchers.fetch_muni_table
fetch_muni_links = _fetchers.fetch_muni_links
fetch_simple_cards = getattr(_fetchers, "fetch_simple_cards", None)
fetch_mls_listings = getattr(_fetchers, "fetch_mls_listings", None)


# ---------- utility ----------
def now_local(tzname):
    tzinfo = tz.gettz(tzname)
    return datetime.datetime.now(tzinfo)

def load_config(path="config.yaml"):
    candidates = []
    if path:
        if os.path.isabs(path):
            candidates.append(path)
        else:
            here = os.path.dirname(os.path.abspath(__file__))
            candidates.extend([
                os.path.join(os.getcwd(), path),
                os.path.join(here, path),
                os.path.join(os.path.dirname(here), path),
            ])
    ordered = []
    seen = set()
    for cand in candidates:
        abs_cand = os.path.abspath(cand)
        if abs_cand not in seen:
            seen.add(abs_cand)
            ordered.append(abs_cand)
    for cand in ordered:
        if os.path.exists(cand):
            with open(cand, "r", encoding="utf-8") as f:
                return yaml.safe_load(f)
    raise FileNotFoundError("Missing config file. Tried: " + ", ".join(ordered))

def ensure_dir(p):
    os.makedirs(p, exist_ok=True)
    return p

def load_seen(path):
    if not os.path.exists(path):
        return {}
    try:
        with open(path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception:
        return {}

def save_seen(path, data):
    ensure_dir(os.path.dirname(path))
    with open(path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False, sort_keys=True)


# ---------- core ----------
def run_city(name, c_cfg, global_cfg, seen_urls_for_city):
    """
    Returns:
      df (DataFrame of candidates with analysis + is_new),
      newly_seen_urls (set[str]) -> to merge back into seen store
    """
    items = []

    # 1) RSS/Atom listings
    rss = (c_cfg.get("rss_listings") or {})
    if rss.get("enabled"):
        items += fetch_rss_listings(rss.get("urls", []))

    # 2) MLS/IDX licensed feed
    mls_city = (c_cfg.get("mls_feed") or c_cfg.get("mls") or {})
    mls_defaults = global_cfg.get("mls_defaults") or {}
    if fetch_mls_listings and (mls_city.get("enabled") or mls_defaults.get("enabled")):
        merged_mls = dict(mls_defaults)
        merged_mls.update(mls_city)
        if merged_mls.get("enabled"):
            merged_mls = dict(merged_mls)  # copy before mutating
            merged_mls.setdefault("source", f"mls:{name}")
            items += fetch_mls_listings(merged_mls)

    # 3) HTML listings (optional, only for pages you're permitted to read)
    html_cfg = (c_cfg.get("html_listings") or {})
    if html_cfg.get("enabled") and fetch_simple_cards:
        items += fetch_simple_cards(
            html_cfg.get("urls", []),
            card_selector=html_cfg.get("card_selector", ".listing-card"),
            title_selector=html_cfg.get("title_selector", "a"),
            desc_selector=html_cfg.get("desc_selector"),
            link_attr=html_cfg.get("link_attr", "href"),
        )

    # 4) Municipal signals
    muni = (c_cfg.get("municipal") or {})
    if muni.get("enabled"):
        for key in ["tax_sales_url", "permits_url"]:
            url = muni.get(key)
            if not url:
                continue
            rows = fetch_muni_table(url)
            if not rows:
                rows = fetch_muni_links(url)
            for r in rows:
                r.setdefault("city", name)
            items += rows

    # Deduplicate
    items = dedupe(items)

    # Filter + analyze
    filtered = []
    newly_seen_urls = set()
    for it in items:
        if looks_like_flip(it, global_cfg):
            it["city"] = name

            # mark new vs seen (based on URL)
            url_key = (it.get("url") or "").strip().lower()
            is_new = url_key not in seen_urls_for_city
            it["is_new"] = bool(is_new)
            if is_new and url_key:
                newly_seen_urls.add(url_key)

            # analysis (needs a price to compute full details; still include if price missing)
            analysis = analyze_item(it, global_cfg)
            if analysis:
                it.update(analysis)
            filtered.append(it)

    df = pd.DataFrame(filtered)
    if not df.empty:
        # sort: new first, then meets_target, then projected profit, then keyword score
        sort_cols = []
        if "is_new" in df.columns: sort_cols.append(("is_new", False))  # True first
        if "meets_target" in df.columns: sort_cols.append(("meets_target", False))
        if "proj_gross_profit" in df.columns: sort_cols.append(("proj_gross_profit", False))
        if "keyword_score" in df.columns: sort_cols.append(("keyword_score", False))

        if sort_cols:
            df = df.sort_values(
                by=[c for c, _ in sort_cols],
                ascending=[asc for _, asc in sort_cols]
            )

        # Reorder columns for readability
        order = [
            "city","is_new","keyword_score","price","address","beds","baths","title","description",
            "url","est_arv","est_reno","closing_costs","carry_costs",
            "proj_gross_profit","proj_margin_pct","meets_target","source"
        ]
        df = df[[c for c in order if c in df.columns] + [c for c in df.columns if c not in order]]

    return df, newly_seen_urls


def main():
    ap = argparse.ArgumentParser(description="Flip finder daily runner")
    ap.add_argument("--cities", help="comma-separated list (default: all in config)")
    ap.add_argument("--only-new", action="store_true", help="report/email only items not seen before")
    ap.add_argument("--config", default="config.yaml", help="path to config.yaml")
    args = ap.parse_args()

    cfg = load_config(args.config)
    tzname = cfg.get("timezone", "America/Toronto")
    today = now_local(tzname).strftime("%Y-%m-%d")

    # City selection
    selected = cfg.get("cities") or {}
    if args.cities:
        chosen = [c.strip() for c in args.cities.split(",") if c.strip()]
        selected = {k: v for k, v in selected.items() if k in chosen}

    # Paths
    here = os.path.dirname(__file__)
    out_dir = ensure_dir(os.path.join(here, "..", "output"))
    state_path = os.path.join(here, "..", ".state", "seen.json")

    # Load seen store: { city: [url1, url2, ...] }
    seen_store = load_seen(state_path)

    # Run cities
    per_city = {}
    combined_list = []
    updated_seen = False

    for name, c_cfg in selected.items():
        seen_urls_for_city = set(u.strip().lower() for u in (seen_store.get(name) or []))
        df, newly_seen = run_city(name, c_cfg, cfg, seen_urls_for_city)

        # Update seen store
        if newly_seen:
            updated_seen = True
            merged = sorted(seen_urls_for_city.union(newly_seen))
            seen_store[name] = merged

        # Optionally filter to only-new for reporting
        if args.only_new and df is not None and not df.empty and "is_new" in df.columns:
            df = df[df["is_new"] == True].copy()

        per_city[name] = df
        if df is not None and not df.empty:
            combined_list.append(df)

    # Save seen store
    if updated_seen:
        save_seen(state_path, seen_store)

    # Write per-city outputs + collect attachments
    attachments = []
    for name, df in per_city.items():
        csv_path, md_path = write_outputs(out_dir, name, today, df)
        for pth in [csv_path, md_path]:
            with open(pth, "rb") as f:
                data = f.read()
            ext = pth.split(".")[-1].lower()
            mime = "text/csv" if ext == "csv" else "text/markdown"
            attachments.append((os.path.basename(pth), data, mime))

    # Combined outputs
    combined_df = pd.concat(combined_list, ignore_index=True) if combined_list else pd.DataFrame()
    comb_csv, comb_md = write_outputs(out_dir, "combined", today, combined_df)
    for pth in [comb_csv, comb_md]:
        with open(pth, "rb") as f:
            data = f.read()
        ext = pth.split(".")[-1].lower()
        mime = "text/csv" if ext == "csv" else "text/markdown"
        attachments.append((os.path.basename(pth), data, mime))

    # Email summary
    total = sum(0 if df is None or df.empty else len(df) for df in per_city.values())
    new_total = 0
    for df in per_city.values():
        if df is not None and not df.empty and "is_new" in df.columns:
            new_total += int(df["is_new"].sum())

    bullets = "".join([
        f"<li><b>{name}</b>: {0 if df is None or df.empty else len(df)}"
        + (f" (<b>new:</b> {int(df['is_new'].sum())})" if df is not None and 'is_new' in df.columns else "")
        + "</li>"
        for name, df in per_city.items()
    ])

    html = f"""
    <h2>Flip Opportunities — {today}</h2>
    <p><b>Total:</b> {total} &nbsp;|&nbsp; <b>New since last run:</b> {new_total}</p>
    <ul>{bullets}</ul>
    <p>CSV/Markdown attached. Edit <code>config.yaml</code> to add more feeds or cities.</p>
    """

    subj = f"Flip Opportunities — {today} (new: {new_total}, total: {total})"
    send_email(cfg.get("email", {}), subject=subj, html_body=html, attachments=attachments)

    print(f"[done] {today}: total={total}, new={new_total}")
    if args.only_new:
        print("[info] --only-new was enabled; outputs/email include only unseen items.")


if __name__ == "__main__":
    main()







