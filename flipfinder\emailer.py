import ssl, smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

def _build_email(subject, html_body, attachments):
    msg = MIMEMultipart()
    msg["Subject"] = subject
    msg.attach(MIMEText(html_body, "html", "utf-8"))
    for fname, content, mime in attachments or []:
        main, sub = mime.split("/", 1)
        part = MIMEBase(main, sub)
        part.set_payload(content)
        encoders.encode_base64(part)
        part.add_header("Content-Disposition", f'attachment; filename="{fname}"')
        msg.attach(part)
    return msg

def send_email(cfg_email, subject, html_body, attachments):
    if not cfg_email.get("enabled", False):
        print("[info] email disabled")
        return
    msg = _build_email(subject, html_body, attachments)
    msg["From"] = cfg_email["from_addr"]
    msg["To"] = ", ".join(cfg_email["to_addrs"])
    if cfg_email.get("use_tls", True):
        server = smtplib.SMTP(cfg_email["smtp_host"], int(cfg_email["smtp_port"]))
        server.starttls(context=ssl.create_default_context())
    else:
        server = smtplib.SMTP_SSL(cfg_email["smtp_host"], int(cfg_email["smtp_port"]))
    server.login(cfg_email["username"], cfg_email["password"])
    server.sendmail(cfg_email["from_addr"], cfg_email["to_addrs"], msg.as_string())
    server.quit()
