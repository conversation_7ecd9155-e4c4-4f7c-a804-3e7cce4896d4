# Flip Opportunities Finder — Multi-City Daily Email

This tool scans **residential/small multi-family** listings for target cities (e.g., KW/Cambridge, Barrie, Newmarket),
flags flip-friendly leads (“as-is”, “handyman”, “needs TLC”, “estate/power of sale”), runs a simple **deal analysis**
(ARV heuristic, reno budget heuristic, projected profit), and emails you a daily report with CSV attachments.

## Quick Start

```bash
python3 -m venv .venv
source .venv/bin/activate   # Windows: .venv\Scripts\activate
pip install -r requirements.txt

# 1) Edit config.yaml (feeds + cities + email)
# 2) Run a test:
python -m flipfinder.runner --cities kw_cambridge,barrie,newmarket
