﻿timezone: America/Toronto
keywords_any:
  - fixer upper
  - power of sale
  - estate sale
price_max: 850000
min_beds: 2
analysis:
  reno_budget_per_bed: 15000
  reno_budget_keyword_bump: 20000
  arv_uplift_percent: 0.12
  carry_months: 4
  carry_percent_apr: 0.07
  closing_percent: 0.05
  conservative_buffer: 0.1
  target_profit_min: 60000
email:
  enabled: true
  smtp_host: "smtp.gmail.com"
  smtp_port: 587
  use_tls: true
  username: "<EMAIL>"
  password: "APP_PASSWORD"   # Gmail App Password, not your normal password
  from_addr: "<EMAIL>"
  to_addrs:
    - "<EMAIL>"
  subject: "Real Estate Deals"
mls_defaults:
  enabled: false  # flip to true after you secure licensed API access
  url: https://your-ddf-or-idx-endpoint.example
  token: YOUR_ACCESS_TOKEN
  timeout: 20
  data_path: data.listings
  field_map:
    title: title
    description: remarks
    url: permalink
    price: list_price
    address: address.full
    beds: details.bedrooms
    baths: details.bathrooms

cities:
  kw_cambridge:
    rss_listings:
      enabled: true
      urls:
        # Realtor.ca search for <PERSON><PERSON> with keyword "handyman"
        - "https://www.realtor.ca/map#ZoomLevel=11&Center=43.45,-80.5&GeoIds=g13&PropertyTypeGroupID=1&TransactionTypeId=2&Keywords=handyman"
        - "https://www.realtor.ca/map#ZoomLevel=11&Center=43.4,-80.3&GeoIds=g13&PropertyTypeGroupID=1&TransactionTypeId=2&Keywords=as-is"
    mls_feed:
      enabled: false
      params:
        City: 'Kitchener'
        Status: 'Active'
      field_map:
        title: title
        description: remarks
        url: permalink
        price: list_price
        address: address.full
        beds: details.bedrooms
        baths: details.bathrooms

    municipal:
      enabled: true
      tax_sales_url: "https://www.kitchener.ca/en/city-services/tax-sales.aspx"
      permits_url:   "https://app2.kitchener.ca/applications/buildingpermits/"

  barrie:
    rss_listings:
      enabled: true
      urls:
        - "https://www.realtor.ca/on/barrie/real-estate?keywords=tlc"
    # Optional: use a private mirror of your target site.
    # Fill in your mirror URLs and CSS selectors, then set enabled: true
    html_listings:
      enabled: false
      urls:
        # Use your mirror endpoints; originals for reference:
        # - https://www.zolo.ca/barrie-real-estate
        # - https://www.realtor.ca/on/barrie/real-estate
        - "https://YOUR-MIRROR-BASE/zolo/barrie"
        - "https://YOUR-MIRROR-BASE/realtor/barrie"
      card_selector: ".listing-card"
      title_selector: ".listing-title a"
      desc_selector: ".listing-description"
      link_attr: "href"
    mls_feed:
      enabled: false
      params:
        City: 'Barrie'
        Status: 'Active'
      field_map: {}

    municipal:
      enabled: true
      tax_sales_url: "https://www.barrie.ca/Living/Property-and-Taxes/Tax-Sales"   # Barrie Tax Sales
      permits_url:   "https://www.barrie.ca/Business/Building-Permits/Pages/Building-Permits.aspx"

  # --- Barrie area (stubs; configure your mirror + enable) ---
  innisfil:
    rss_listings:
      enabled: false
      urls: []
    html_listings:
      enabled: false
      urls:
        - "https://YOUR-MIRROR-BASE/innisfil"
      card_selector: ".listing-card"
      title_selector: ".listing-title a"
      desc_selector: ".listing-description"
      link_attr: "href"
    municipal:
      enabled: false
      tax_sales_url: ""
      permits_url: ""

  orillia:
    rss_listings:
      enabled: false
      urls: []
    html_listings:
      enabled: false
      urls:
        - "https://YOUR-MIRROR-BASE/orillia"
      card_selector: ".listing-card"
      title_selector: ".listing-title a"
      desc_selector: ".listing-description"
      link_attr: "href"
    municipal:
      enabled: false
      tax_sales_url: ""
      permits_url: ""

  springwater:
    rss_listings:
      enabled: false
      urls: []
    html_listings:
      enabled: false
      urls:
        - "https://YOUR-MIRROR-BASE/springwater"
      card_selector: ".listing-card"
      title_selector: ".listing-title a"
      desc_selector: ".listing-description"
      link_attr: "href"
    municipal:
      enabled: false
      tax_sales_url: ""
      permits_url: ""

  essa:
    rss_listings:
      enabled: false
      urls: []
    html_listings:
      enabled: false
      urls:
        - "https://YOUR-MIRROR-BASE/essa"
      card_selector: ".listing-card"
      title_selector: ".listing-title a"
      desc_selector: ".listing-description"
      link_attr: "href"
    municipal:
      enabled: false
      tax_sales_url: ""
      permits_url: ""

  oro_medonte:
    rss_listings:
      enabled: false
      urls: []
    html_listings:
      enabled: false
      urls:
        - "https://YOUR-MIRROR-BASE/oro-medonte"
      card_selector: ".listing-card"
      title_selector: ".listing-title a"
      desc_selector: ".listing-description"
      link_attr: "href"
    municipal:
      enabled: false
      tax_sales_url: ""
      permits_url: ""

  bradford:
    rss_listings:
      enabled: false
      urls: []
    html_listings:
      enabled: false
      urls:
        - "https://YOUR-MIRROR-BASE/bradford"
      card_selector: ".listing-card"
      title_selector: ".listing-title a"
      desc_selector: ".listing-description"
      link_attr: "href"
    municipal:
      enabled: false
      tax_sales_url: ""
      permits_url: ""

  wasaga_beach:
    rss_listings:
      enabled: false
      urls: []
    html_listings:
      enabled: false
      urls:
        - "https://YOUR-MIRROR-BASE/wasaga-beach"
      card_selector: ".listing-card"
      title_selector: ".listing-title a"
      desc_selector: ".listing-description"
      link_attr: "href"
    municipal:
      enabled: false
      tax_sales_url: ""
      permits_url: ""

  collingwood:
    rss_listings:
      enabled: false
      urls: []
    html_listings:
      enabled: false
      urls:
        - "https://YOUR-MIRROR-BASE/collingwood"
      card_selector: ".listing-card"
      title_selector: ".listing-title a"
      desc_selector: ".listing-description"
      link_attr: "href"
    municipal:
      enabled: false
      tax_sales_url: ""
      permits_url: ""

  midland:
    rss_listings:
      enabled: false
      urls: []
    html_listings:
      enabled: false
      urls:
        - "https://YOUR-MIRROR-BASE/midland"
      card_selector: ".listing-card"
      title_selector: ".listing-title a"
      desc_selector: ".listing-description"
      link_attr: "href"
    municipal:
      enabled: false
      tax_sales_url: ""
      permits_url: ""

  alliston:
    rss_listings:
      enabled: false
      urls: []
    html_listings:
      enabled: false
      urls:
        - "https://YOUR-MIRROR-BASE/alliston"
      card_selector: ".listing-card"
      title_selector: ".listing-title a"
      desc_selector: ".listing-description"
      link_attr: "href"
    municipal:
      enabled: false
      tax_sales_url: ""
      permits_url: ""

  orangeville:
    rss_listings:
      enabled: false
      urls: []
    html_listings:
      enabled: false
      urls:
        - "https://YOUR-MIRROR-BASE/orangeville"
      card_selector: ".listing-card"
      title_selector: ".listing-title a"
      desc_selector: ".listing-description"
      link_attr: "href"
    municipal:
      enabled: false
      tax_sales_url: ""
      permits_url: ""

  newmarket:
    rss_listings:
      enabled: true
      urls:
        - "https://www.realtor.ca/on/newmarket/real-estate?keywords=estate%20sale"
    mls_feed:
      enabled: false
      params:
        City: 'Newmarket'
        Status: 'Active'
      field_map: {}

    municipal:
      enabled: true
      tax_sales_url: "https://www.newmarket.ca/en/town-hall/Property-Tax-Sales.aspx"
      permits_url:   "https://www.newmarket.ca/en/building-and-renovating/Building-Permits.aspx"
