import math

try:
    from .filters import money_to_float, score_keywords
except ImportError:  # pragma: no cover - script execution
    from filters import money_to_float, score_keywords

def estimate_reno_budget(item, cfg):
    # Heuristic: base on beds if present; bump if keywords indicate heavier work
    beds = item.get("beds")
    base = cfg["analysis"].get("reno_budget_per_bed", 15000)
    bump = cfg["analysis"].get("reno_budget_keyword_bump", 20000)
    est = (float(beds) if beds else 2) * base
    text = (item.get("title","") + " " + item.get("description","")).lower()
    if any(k in text for k in ["as-is","handyman","needs tlc","power of sale","estate"]):
        est += bump
    return est

def estimate_arv(item, cfg):
    # If you don't have comps, assume uplift vs current price
    price = money_to_float(item.get("price"))
    if not price: return None
    uplift = cfg["analysis"].get("arv_uplift_percent", 0.12)
    return price * (1 + float(uplift))

def carrying_costs(item, cfg):
    price = money_to_float(item.get("price")) or 0
    months = cfg["analysis"].get("carry_months", 4)
    apr = cfg["analysis"].get("carry_percent_apr", 0.07)
    return price * (apr * months/12.0)

def closing_costs(item, cfg):
    price = money_to_float(item.get("price")) or 0
    return price * cfg["analysis"].get("closing_percent", 0.05)

def analyze_item(item, cfg):
    price = money_to_float(item.get("price"))
    if not price:
        return None  # cannot analyze without a price
    reno = estimate_reno_budget(item, cfg)
    arv  = estimate_arv(item, cfg) or price
    closing = closing_costs(item, cfg)
    carry   = carrying_costs(item, cfg)
    buffer  = cfg["analysis"].get("conservative_buffer", 0.10)
    total_costs = price + reno + closing + carry
    total_costs *= (1 + buffer)
    gross_profit = arv - total_costs
    margin = gross_profit / arv if arv else 0
    want_min = cfg["analysis"].get("target_profit_min", 60000)

    analysis = {
        "est_reno": round(reno),
        "est_arv": round(arv),
        "closing_costs": round(closing),
        "carry_costs": round(carry),
        "buffered_total_costs": round(total_costs),
        "proj_gross_profit": round(gross_profit),
        "proj_margin_pct": round(margin*100, 1),
        "meets_target": bool(gross_profit >= want_min),
        "keyword_score": score_keywords(item),
    }
    return analysis
