timezone: "America/Toronto"

# Flip signals
keywords_any:
  - "as-is"
  - "handyman"
  - "handyman special"
  - "needs tlc"
  - "fixer upper"
  - "estate sale"
  - "power of sale"
  - "bring your contractor"
  - "renovator"
  - "investor special"

price_max: 900000
min_beds: 2

# Heuristic analysis knobs
analysis:
  target_profit_min: 60000        # minimum profit you want per flip
  closing_percent: 0.05           # buy+sell closing/friction cost ~5%
  carry_months: 4                  # months you expect to hold
  carry_percent_apr: 0.07         # annual effective interest on capital (7%)
  reno_budget_per_bed: 15000      # fallback if no better signal
  reno_budget_keyword_bump: 20000 # add this if keywords suggest heavier work
  arv_uplift_percent: 0.12        # after-renovation uplift vs current price if comps unknown (12%)
  conservative_buffer: 0.10       # 10% buffer on total costs

cities:
  kw_cambridge:
    rss_listings:
      enabled: true
      urls:
        - "PUT_A_VALID_RSS_OR_ATOM_FEED_URL_HERE"
    municipal:
      enabled: true
      tax_sales_url: "OPTIONAL_TAX_SALES_URL"
      permits_url:   "OPTIONAL_PERMITS_URL"

  barrie:
    rss_listings:
      enabled: true
      urls:
        - "PUT_A_VALID_RSS_OR_ATOM_FEED_URL_HERE"
    # Optional: HTML listings via your private mirror of allowed sources
    html_listings:
      enabled: false
      urls:
        # Originals for reference (configure mirror equivalents):
        # - https://www.zolo.ca/barrie-real-estate
        # - https://www.realtor.ca/on/barrie/real-estate
        - "https://YOUR-MIRROR-BASE/zolo/barrie"
        - "https://YOUR-MIRROR-BASE/realtor/barrie"
      card_selector: ".listing-card"
      title_selector: ".listing-title a"
      desc_selector: ".listing-description"
      link_attr: "href"
    municipal:
      enabled: true
      tax_sales_url: "https://www.barrie.ca/Living/Property-and-Taxes/Tax-Sales"
      permits_url: "https://www.barrie.ca/Business/Building-Permits/Pages/Building-Permits.aspx"

  newmarket:
    rss_listings:
      enabled: true
      urls:
        - "PUT_A_VALID_RSS_OR_ATOM_FEED_URL_HERE"
    municipal:
      enabled: false
      tax_sales_url: ""
      permits_url: ""

email:
  enabled: false
  smtp_host: "smtp.example.com"
  smtp_port: 587
  use_tls: true
  username: "<EMAIL>"
  password: "APP_PASSWORD_OR_TOKEN"
  from_addr: "<EMAIL>"
  to_addrs:
    - "<EMAIL>"
