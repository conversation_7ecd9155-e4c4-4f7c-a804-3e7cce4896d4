﻿import os, json, pandas as pd

def to_markdown_table(df: pd.DataFrame, max_rows=30):
    if df is None or df.empty:
        return "_No opportunities found._"
    cols = [
        "city","keyword_score","price","address","beds","baths","title","url",
        "est_arv","est_reno","closing_costs","carry_costs","proj_gross_profit","proj_margin_pct","meets_target","source"
    ]
    exist = [c for c in cols if c in df.columns]
    header = ["| " + " | ".join([c.replace('_',' ').title() for c in exist]) + " |",
              "|" + "|".join([":--" if i==0 else ":-:" for i,_ in enumerate(exist)]) + "|"]
    lines = header[:]
    for _, r in df.head(max_rows).iterrows():
        row = []
        for c in exist:
            v = r.get(c, "")
            if c == "url" and v:
                row.append(f"[link]({v})")
            else:
                row.append(str(v))
        lines.append("| " + " | ".join(row) + " |")
    if len(df) > max_rows:
        lines.append(f"\n_+{len(df)-max_rows} more omitted..._")
    return "\n".join(lines)

def write_outputs(out_dir, city_name, today, df):
    os.makedirs(out_dir, exist_ok=True)
    csv_path = os.path.join(out_dir, f"{city_name}_opps_{today}.csv")
    md_path  = os.path.join(out_dir, f"{city_name}_opps_{today}.md")
    (df if df is not None and not df.empty else pd.DataFrame()).to_csv(csv_path, index=False)
    md = to_markdown_table(df)
    with open(md_path, "w", encoding="utf-8") as f:
        f.write(md)
    return csv_path, md_path

