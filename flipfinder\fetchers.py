import re, sys, requests, feedparser
from bs4 import BeautifulSoup
# ---- simple HTML list fetcher (use ONLY on pages you're permitted to read) ----
import requests
from bs4 import BeautifulSoup

def fetch_simple_cards(urls, card_selector, title_selector="a", desc_selector=None, link_attr="href"):
    items = []
    for url in urls or []:
        try:
            r = requests.get(url, timeout=20)
            r.raise_for_status()
            soup = BeautifulSoup(r.text, "html.parser")
            for card in soup.select(card_selector):
                title_el = card.select_one(title_selector)
                if not title_el: 
                    continue
                title = title_el.get_text(strip=True)
                desc  = card.select_one(desc_selector).get_text(strip=True) if desc_selector else ""
                link  = title_el.get(link_attr) or url
                items.append({
                    "title": title, "description": desc, "url": link if str(link).startswith("http") else url,
                    "price": None, "address": "", "beds": None, "baths": None,
                    "source": f"html:{url}"
                })
        except Exception as ex:
            print(f"[warn] simple_cards failed {url}: {ex}", file=sys.stderr)
    return items

def parse_price(text):
    if not text: return None
    m = re.search(r"\$[\d,]+", str(text))
    return m.group(0) if m else None

def fetch_rss_listings(urls):
    items = []
    for url in urls or []:
        try:
            d = feedparser.parse(url)
            for e in d.entries:
                title = e.get("title","")
                desc  = e.get("summary","")
                link  = e.get("link","")
                # Address/beds/baths often missing in feeds; leave blank or enrich later.
                price = parse_price(title + " " + desc)
                items.append({
                    "title": title, "description": desc, "url": link,
                    "price": price, "address": "", "beds": None, "baths": None,
                    "source": f"rss:{url}"
                })
        except Exception as ex:
            print(f"[warn] RSS fetch failed {url}: {ex}", file=sys.stderr)
    return items

def fetch_muni_table(url, row_sel="table tr", cell_sel="td"):
    if not url: return []
    try:
        r = requests.get(url, timeout=20)
        r.raise_for_status()
        soup = BeautifulSoup(r.text, "html.parser")
        rows = []
        for tr in soup.select(row_sel):
            cells = [c.get_text(strip=True) for c in tr.select(cell_sel)]
            if len(cells) >= 2:
                rows.append({
                    "title": cells[0], "description": " | ".join(cells[1:]),
                    "url": url, "price": None, "address": "", "beds": None, "baths": None,
                    "source": f"municipal:{url}"
                })
        return rows
    except Exception as ex:
        print(f"[warn] municipal table failed {url}: {ex}", file=sys.stderr)
        return []

def fetch_muni_links(url, container_sel="main", link_sel="a"):
    if not url: return []
    try:
        r = requests.get(url, timeout=20)
        r.raise_for_status()
        soup = BeautifulSoup(r.text, "html.parser")
        cont = soup.select_one(container_sel) or soup
        out = []
        for a in cont.select(link_sel):
            text = a.get_text(strip=True)
            href = a.get("href","")
            if not text or not href: continue
            out.append({
                "title": text, "description": "", "url": href if href.startswith("http") else url,
                "price": None, "address": "", "beds": None, "baths": None,
                "source": f"municipal:{url}"
            })
        return out
    except Exception as ex:
        print(f"[warn] municipal links failed {url}: {ex}", file=sys.stderr)
        return []

def _extract_path(data, path):
    if not path:
        return data
    current = data
    for part in path.split('.'):
        if isinstance(current, dict):
            current = current.get(part)
        else:
            return None
    return current

def fetch_mls_listings(cfg):
    """Fetch MLS/IDX listings from an authenticated feed."""
    if not cfg or not cfg.get('enabled'):
        return []

    url = cfg.get('url')
    if not url:
        print('[warn] MLS feed enabled but no url provided', file=sys.stderr)
        return []

    headers = dict(cfg.get('headers') or {})
    token = cfg.get('token')
    if token and 'Authorization' not in headers:
        headers['Authorization'] = f'Bearer {token}'

    timeout = cfg.get('timeout', 20)
    params = cfg.get('params') or {}

    try:
        resp = requests.get(url, headers=headers, params=params, timeout=timeout)
        resp.raise_for_status()
    except Exception as ex:
        print(f'[warn] MLS fetch failed {url}: {ex}', file=sys.stderr)
        return []

    try:
        payload = resp.json()
    except ValueError:
        print(f'[warn] MLS feed {url} did not return JSON', file=sys.stderr)
        return []

    data_path = cfg.get('data_path')
    items_raw = _extract_path(payload, data_path) if data_path else payload
    if not items_raw:
        return []

    if isinstance(items_raw, dict):
        items_iter = items_raw.values()
    else:
        items_iter = items_raw

    field_map = cfg.get('field_map') or {}

    def get_field(item, key, default=''):
        path = field_map.get(key, key)
        if isinstance(path, str):
            value = _extract_path(item, path)
        else:
            value = item.get(path)
        return value if value is not None else default

    out = []
    for entry in items_iter:
        if not isinstance(entry, dict):
            continue
        title = get_field(entry, 'title', '')
        desc = get_field(entry, 'description', '')
        link = get_field(entry, 'url', '')
        price_val = get_field(entry, 'price')
        if isinstance(price_val, (int, float)):
            price = f'${price_val:,.0f}'
        else:
            price = parse_price(price_val)
        out.append({
            'title': str(title) if title is not None else '',
            'description': str(desc) if desc is not None else '',
            'url': str(link) if link else '',
            'price': price,
            'address': get_field(entry, 'address', ''),
            'beds': get_field(entry, 'beds'),
            'baths': get_field(entry, 'baths'),
            'source': cfg.get('source', 'mls')
        })
    return out
