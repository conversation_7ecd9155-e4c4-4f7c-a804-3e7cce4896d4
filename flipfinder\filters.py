def _text(item):
    return (item.get("title","") + " " + item.get("description","")).lower()

def has_any_keyword(item, keywords_any):
    t = _text(item)
    return any(k.lower() in t for k in (keywords_any or []))

def money_to_float(s):
    if not s: return None
    s = str(s).replace("$","").replace(",","").strip()
    try: return float(s)
    except: return None

def looks_like_flip(item, cfg):
    if not has_any_keyword(item, cfg.get("keywords_any", [])):
        return False
    price_max = cfg.get("price_max")
    if price_max and item.get("price"):
        val = money_to_float(item["price"])
        if val and val > float(price_max):
            return False
    min_beds = cfg.get("min_beds", 0)
    beds = item.get("beds")
    try:
        if beds is not None and float(beds) < float(min_beds):
            return False
    except: pass
    return True

def score_keywords(item):
    t = _text(item)
    score = 0
    for kw, pts in [
        ("power of sale", 40),
        ("as-is", 30),
        ("handyman", 30),
        ("needs tlc", 25),
        ("fixer upper", 25),
        ("estate", 20),
        ("bring your contractor", 15),
        ("renovator", 15),
        ("investor", 10),
    ]:
        if kw in t:
            score += pts
    price = money_to_float(item.get("price"))
    if price and price < 500000:
        score += 10
    return min(score, 100)

def dedupe(items):
    seen, out = set(), []
    for it in items:
        key = (it.get("address","").lower().strip(), it.get("url","").lower().strip())
        if key not in seen:
            seen.add(key); out.append(it)
    return out
